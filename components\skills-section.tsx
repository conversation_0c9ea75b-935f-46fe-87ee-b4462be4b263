"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Globe, Layout, Smartphone, Zap, Cpu, PenTool, Code, Server, Database } from "lucide-react"
import { motion } from "framer-motion"

export function SkillsSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [isInView, setIsInView] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  }
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 },
    },
  }
  
  // Check if section is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
        }
      },
      { threshold: 0.1 }
    )
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [])

  const skills = [
    {
      category: "Frontend",
      icon: <Layout className="w-10 h-10 text-primary" />,
      technologies: ["HTML", "CSS", "JavaScript", "React", "Responsive Design"],
    },
    {
      category: "UI/UX",
      icon: <PenTool className="w-10 h-10 text-primary" />,
      technologies: ["Figma", "User Research", "Wireframing", "Prototyping"],
    },
    {
      category: "Tools",
      icon: <Zap className="w-10 h-10 text-primary" />,
      technologies: ["Git", "Power BI", "VS Code", "Chrome DevTools"],
    },
    {
      category: "Learning",
      icon: <Cpu className="w-10 h-10 text-primary" />,
      technologies: ["Node.js", "Express", "MongoDB", "DevOps", "AWS"],
    },
    {
      category: "Soft Skills",
      icon: <Globe className="w-10 h-10 text-primary" />,
      technologies: ["Problem Solving", "Team Collaboration", "Communication", "Time Management"],
    },
    {
      category: "Other",
      icon: <Smartphone className="w-10 h-10 text-primary" />,
      technologies: ["Prompt Engineering", "SEO Basics", "Performance Optimization", "Accessibility"],
    },
  ]

  return (
    <section id="skills" className="py-20 relative overflow-hidden" ref={sectionRef}>
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-3 neo-text bg-clip-text text-transparent bg-gradient-to-r from-white to-white/90 font-space-grotesk">
            My Skills
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-primary to-blue-500 mx-auto mb-6 neo-glow rounded-full"></div>
          <p className="text-white/80 max-w-2xl mx-auto text-lg">
            I've developed expertise in various technologies and continue to expand my knowledge in new areas.
          </p>
        </motion.div>

        {/* Tree Structure Container - Like your drawing! */}
        <div className="relative max-w-7xl mx-auto">
          {/* SVG Tree - Trunk from title flowing down and branching out */}
          <svg
            className="absolute top-16 left-0 w-full h-full pointer-events-none z-0"
            viewBox="0 0 1400 1200"
            preserveAspectRatio="xMidYMid meet"
          >
            <defs>
              <linearGradient id="trunkGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.9" />
                <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.7" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.5" />
              </linearGradient>
              <linearGradient id="branchGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.4" />
              </linearGradient>
              <linearGradient id="branchGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.4" />
              </linearGradient>
              <linearGradient id="branchGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.8" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.4" />
              </linearGradient>
            </defs>

            {/* Main Trunk - flowing down from title */}
            <motion.path
              d="M 700 80 Q 700 120 700 200"
              stroke="url(#trunkGradient)"
              strokeWidth="6"
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]"
            />

            {/* Branch Junction Point */}
            <motion.circle
              cx="700"
              cy="200"
              r="4"
              fill="#3b82f6"
              initial={{ scale: 0, opacity: 0 }}
              animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
              transition={{ duration: 0.5, delay: 1.3 }}
              className="drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]"
            />

            {/* Tree branches flowing outward like your drawing */}
            {/* Frontend Branch - Far Left */}
            <motion.path
              d="M 700 200 Q 550 280 200 400"
              stroke={hoveredIndex === 0 ? "#3b82f6" : "url(#branchGradient1)"}
              strokeWidth={hoveredIndex === 0 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.5, delay: 1.5 }}
              className={hoveredIndex === 0 ? "drop-shadow-[0_0_15px_rgba(59,130,246,0.9)]" : ""}
            />

            {/* UI/UX Branch - Center Left */}
            <motion.path
              d="M 700 200 Q 600 300 450 450"
              stroke={hoveredIndex === 1 ? "#8b5cf6" : "url(#branchGradient2)"}
              strokeWidth={hoveredIndex === 1 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.5, delay: 1.7 }}
              className={hoveredIndex === 1 ? "drop-shadow-[0_0_15px_rgba(139,92,246,0.9)]" : ""}
            />

            {/* Tools Branch - Center Right */}
            <motion.path
              d="M 700 200 Q 800 300 950 450"
              stroke={hoveredIndex === 2 ? "#06b6d4" : "url(#branchGradient3)"}
              strokeWidth={hoveredIndex === 2 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.5, delay: 1.9 }}
              className={hoveredIndex === 2 ? "drop-shadow-[0_0_15px_rgba(6,182,212,0.9)]" : ""}
            />

            {/* Learning Branch - Far Left Bottom */}
            <motion.path
              d="M 700 200 Q 500 400 150 650"
              stroke={hoveredIndex === 3 ? "#3b82f6" : "url(#branchGradient1)"}
              strokeWidth={hoveredIndex === 3 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.8, delay: 2.1 }}
              className={hoveredIndex === 3 ? "drop-shadow-[0_0_15px_rgba(59,130,246,0.9)]" : ""}
            />

            {/* Soft Skills Branch - Center Bottom */}
            <motion.path
              d="M 700 200 Q 700 400 700 650"
              stroke={hoveredIndex === 4 ? "#8b5cf6" : "url(#branchGradient2)"}
              strokeWidth={hoveredIndex === 4 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.8, delay: 2.3 }}
              className={hoveredIndex === 4 ? "drop-shadow-[0_0_15px_rgba(139,92,246,0.9)]" : ""}
            />

            {/* Other Branch - Far Right Bottom */}
            <motion.path
              d="M 700 200 Q 900 400 1250 650"
              stroke={hoveredIndex === 5 ? "#06b6d4" : "url(#branchGradient3)"}
              strokeWidth={hoveredIndex === 5 ? "5" : "3"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 1.8, delay: 2.5 }}
              className={hoveredIndex === 5 ? "drop-shadow-[0_0_15px_rgba(6,182,212,0.9)]" : ""}
            />

            {/* Flowing energy particles along branches */}
            {isInView && (
              <>
                {/* Trunk particle */}
                <motion.circle
                  r="4"
                  fill="#3b82f6"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0.5],
                    cy: [80, 120, 200]
                  }}
                  cx="700"
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: 3,
                    ease: "easeInOut"
                  }}
                />

                {/* Branch particles */}
                <motion.circle
                  r="3"
                  fill="#3b82f6"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [700, 550, 200],
                    cy: [200, 280, 400]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    delay: 4,
                    ease: "easeInOut"
                  }}
                />

                <motion.circle
                  r="2.5"
                  fill="#8b5cf6"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [700, 600, 450],
                    cy: [200, 300, 450]
                  }}
                  transition={{
                    duration: 3.5,
                    repeat: Infinity,
                    delay: 5,
                    ease: "easeInOut"
                  }}
                />

                <motion.circle
                  r="3"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [700, 900, 1250],
                    cy: [200, 400, 650]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    delay: 6,
                    ease: "easeInOut"
                  }}
                />
              </>
            )}
          </svg>

          {/* Skills Cards in Natural Tree Layout */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="relative z-10 pt-24"
          >
            {/* Tree Branch Layout - Like your drawing! */}
            {/* Top Row - 3 cards spread wide like upper branches */}
            <div className="flex flex-wrap justify-between items-start mb-32 px-8 max-w-7xl mx-auto">
              {/* Frontend - Far Left */}
              <div className="w-full md:w-80 lg:w-72 mb-8 md:mb-0">
                {skills.slice(0, 1).map((skill, index) => (
                  <motion.div key={index} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-blue-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-blue-500/10 text-blue-300 px-3 py-1 rounded-full text-xs border border-blue-500/20 transition-all duration-300 hover:bg-blue-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* UI/UX - Center */}
              <div className="w-full md:w-80 lg:w-72 mb-8 md:mb-0">
                {skills.slice(1, 2).map((skill, index) => (
                  <motion.div key={index + 1} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 1
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 1)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/30 to-blue-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 1 ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-purple-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-purple-500/10 text-purple-300 px-3 py-1 rounded-full text-xs border border-purple-500/20 transition-all duration-300 hover:bg-purple-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Tools - Far Right */}
              <div className="w-full md:w-80 lg:w-72">
                {skills.slice(2, 3).map((skill, index) => (
                  <motion.div key={index + 2} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 2
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 2)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-cyan-500/30 to-green-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 2 ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-cyan-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-cyan-500/10 text-cyan-300 px-3 py-1 rounded-full text-xs border border-cyan-500/20 transition-all duration-300 hover:bg-cyan-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Bottom Row - 3 cards spread even wider like lower branches */}
            <div className="flex flex-wrap justify-between items-start px-4 max-w-8xl mx-auto">
              {/* Learning - Far Left */}
              <div className="w-full md:w-80 lg:w-72 mb-8 md:mb-0">
                {skills.slice(3, 4).map((skill, index) => (
                  <motion.div key={index + 3} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 3
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 3)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 3 ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-blue-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-blue-500/10 text-blue-300 px-3 py-1 rounded-full text-xs border border-blue-500/20 transition-all duration-300 hover:bg-blue-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Soft Skills - Center */}
              <div className="w-full md:w-80 lg:w-72 mb-8 md:mb-0">
                {skills.slice(4, 5).map((skill, index) => (
                  <motion.div key={index + 4} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 4
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 4)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/30 to-blue-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 4 ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-purple-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-purple-500/10 text-purple-300 px-3 py-1 rounded-full text-xs border border-purple-500/20 transition-all duration-300 hover:bg-purple-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Other - Far Right */}
              <div className="w-full md:w-80 lg:w-72">
                {skills.slice(5, 6).map((skill, index) => (
                  <motion.div key={index + 5} variants={itemVariants}>
                    <Card
                      className={`group relative overflow-hidden transition-all duration-500 ${
                        hoveredIndex === index + 5
                          ? "neo-glow transform -translate-y-2 scale-105"
                          : "hover:transform hover:-translate-y-1"
                      }`}
                      onMouseEnter={() => setHoveredIndex(index + 5)}
                      onMouseLeave={() => setHoveredIndex(null)}
                    >
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-cyan-500/30 to-green-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                        <CardContent className="p-6">
                          <div className="flex flex-col items-center text-center">
                            <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === index + 5 ? "scale-110 neo-glow" : ""}`}>
                              {skill.icon}
                            </div>
                            <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-cyan-300 transition-colors duration-300 font-space-grotesk">
                              {skill.category}
                            </h3>
                            <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                              {skill.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="bg-cyan-500/10 text-cyan-300 px-3 py-1 rounded-full text-xs border border-cyan-500/20 transition-all duration-300 hover:bg-cyan-500/20">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Removed floating geometric shapes - galaxy background provides the cosmic effect */}
    </section>
  )
}

