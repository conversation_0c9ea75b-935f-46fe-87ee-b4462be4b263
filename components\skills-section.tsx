"use client"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Globe, Layout, Smartphone, Zap, Cpu, PenTool, Code, Server, Database } from "lucide-react"
import { motion } from "framer-motion"

export function SkillsSection() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null)
  const [isInView, setIsInView] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  }
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100 },
    },
  }
  
  // Check if section is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
        }
      },
      { threshold: 0.1 }
    )
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }
    
    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current)
      }
    }
  }, [])

  const skills = [
    {
      category: "Frontend",
      icon: <Layout className="w-10 h-10 text-primary" />,
      technologies: ["HTML", "CSS", "JavaScript", "React", "Responsive Design"],
    },
    {
      category: "UI/UX",
      icon: <PenTool className="w-10 h-10 text-primary" />,
      technologies: ["Figma", "User Research", "Wireframing", "Prototyping"],
    },
    {
      category: "Tools",
      icon: <Zap className="w-10 h-10 text-primary" />,
      technologies: ["Git", "Power BI", "VS Code", "Chrome DevTools"],
    },
    {
      category: "Learning",
      icon: <Cpu className="w-10 h-10 text-primary" />,
      technologies: ["Node.js", "Express", "MongoDB", "DevOps", "AWS"],
    },
    {
      category: "Soft Skills",
      icon: <Globe className="w-10 h-10 text-primary" />,
      technologies: ["Problem Solving", "Team Collaboration", "Communication", "Time Management"],
    },
    {
      category: "Other",
      icon: <Smartphone className="w-10 h-10 text-primary" />,
      technologies: ["Prompt Engineering", "SEO Basics", "Performance Optimization", "Accessibility"],
    },
  ]

  return (
    <section id="skills" className="py-20 relative overflow-hidden" ref={sectionRef}>
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 left-0 w-1/3 h-1/3 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/3 h-1/3 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 neo-text bg-clip-text text-transparent bg-gradient-to-r from-white to-white/90 font-space-grotesk">
            My Skills
          </h2>
          <div className="w-24 h-1.5 bg-gradient-to-r from-primary to-blue-500 mx-auto neo-glow rounded-full"></div>
        </motion.div>

        {/* Tree Structure Container - Direct branches from root! */}
        <div className="relative max-w-6xl mx-auto min-h-[800px]">
          {/* SVG Tree - Direct branches from My Skills title to each card */}
          <svg
            className="absolute top-0 left-0 w-full h-full pointer-events-none z-0"
            viewBox="0 0 1200 800"
            preserveAspectRatio="xMidYMid meet"
          >
            <defs>
              <linearGradient id="branchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.9" />
                <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.6" />
              </linearGradient>
            </defs>

            {/* Root node indicator at title position */}
            <motion.circle
              cx="600"
              cy="50"
              r="8"
              fill="#3b82f6"
              initial={{ scale: 0, opacity: 0 }}
              animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="drop-shadow-[0_0_15px_rgba(59,130,246,1)]"
            />

            {/* Direct branches from My Skills title to each card */}
            {/* Frontend Branch - Top Left */}
            <motion.path
              d="M 600 50 Q 400 120 180 280"
              stroke={hoveredIndex === 0 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 0 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 0.8 }}
              className={hoveredIndex === 0 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* UI/UX Branch - Top Right */}
            <motion.path
              d="M 600 50 Q 800 120 1020 280"
              stroke={hoveredIndex === 1 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 1 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 1.0 }}
              className={hoveredIndex === 1 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* Tools Branch - Middle Left */}
            <motion.path
              d="M 600 50 Q 350 200 140 430"
              stroke={hoveredIndex === 2 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 2 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 1.2 }}
              className={hoveredIndex === 2 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* Learning Branch - Middle Right */}
            <motion.path
              d="M 600 50 Q 850 200 1060 430"
              stroke={hoveredIndex === 3 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 3 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 1.4 }}
              className={hoveredIndex === 3 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* Soft Skills Branch - Bottom Left */}
            <motion.path
              d="M 600 50 Q 300 300 180 580"
              stroke={hoveredIndex === 4 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 4 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 1.6 }}
              className={hoveredIndex === 4 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* Other Branch - Bottom Right */}
            <motion.path
              d="M 600 50 Q 900 300 1020 580"
              stroke={hoveredIndex === 5 ? "#06b6d4" : "url(#branchGradient)"}
              strokeWidth={hoveredIndex === 5 ? "4" : "2"}
              fill="none"
              initial={{ pathLength: 0, opacity: 0 }}
              animate={isInView ? { pathLength: 1, opacity: 1 } : { pathLength: 0, opacity: 0 }}
              transition={{ duration: 2, delay: 1.8 }}
              className={hoveredIndex === 5 ? "drop-shadow-[0_0_15px_rgba(6,182,212,1)]" : "drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]"}
            />

            {/* Flowing energy particles along branches */}
            {isInView && (
              <>
                {/* Frontend branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 400, 180],
                    cy: [50, 120, 280]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: 3,
                    ease: "easeInOut"
                  }}
                />

                {/* UI/UX branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 800, 1020],
                    cy: [50, 120, 280]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: 3.5,
                    ease: "easeInOut"
                  }}
                />

                {/* Tools branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 350, 140],
                    cy: [50, 200, 430]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    delay: 4,
                    ease: "easeInOut"
                  }}
                />

                {/* Learning branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 850, 1060],
                    cy: [50, 200, 430]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    delay: 4.5,
                    ease: "easeInOut"
                  }}
                />

                {/* Soft Skills branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 300, 180],
                    cy: [50, 300, 580]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    delay: 5,
                    ease: "easeInOut"
                  }}
                />

                {/* Other branch particle */}
                <motion.circle
                  r="2"
                  fill="#06b6d4"
                  initial={{ opacity: 0 }}
                  animate={{
                    opacity: [0, 1, 0],
                    cx: [600, 900, 1020],
                    cy: [50, 300, 580]
                  }}
                  transition={{
                    duration: 5,
                    repeat: Infinity,
                    delay: 5.5,
                    ease: "easeInOut"
                  }}
                />
              </>
            )}
          </svg>

          {/* Skills Cards Floating in Tree Layout */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="relative z-10 pt-16"
          >
            {/* Frontend Card - Top Left */}
            <motion.div
              className="absolute"
              style={{ left: '50px', top: '230px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -8, 0],
                transition: { duration: 4, repeat: Infinity, ease: "easeInOut" }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 0
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(0)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 0 ? "scale-110 neo-glow" : ""}`}>
                        {skills[0].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-blue-300 transition-colors duration-300 font-space-grotesk">
                        {skills[0].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[0].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-blue-500/10 text-blue-300 px-3 py-1 rounded-full text-xs border border-blue-500/20 transition-all duration-300 hover:bg-blue-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>

            {/* UI/UX Card - Top Right */}
            <motion.div
              className="absolute"
              style={{ right: '50px', top: '230px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -10, 0],
                transition: { duration: 5, repeat: Infinity, ease: "easeInOut", delay: 0.5 }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 1
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(1)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/30 to-blue-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 1 ? "scale-110 neo-glow" : ""}`}>
                        {skills[1].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-purple-300 transition-colors duration-300 font-space-grotesk">
                        {skills[1].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[1].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-purple-500/10 text-purple-300 px-3 py-1 rounded-full text-xs border border-purple-500/20 transition-all duration-300 hover:bg-purple-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>

            {/* Tools Card - Middle Left */}
            <motion.div
              className="absolute"
              style={{ left: '20px', top: '380px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -6, 0],
                transition: { duration: 4.5, repeat: Infinity, ease: "easeInOut", delay: 1 }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 2
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(2)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-cyan-500/30 to-green-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 2 ? "scale-110 neo-glow" : ""}`}>
                        {skills[2].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-cyan-300 transition-colors duration-300 font-space-grotesk">
                        {skills[2].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[2].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-cyan-500/10 text-cyan-300 px-3 py-1 rounded-full text-xs border border-cyan-500/20 transition-all duration-300 hover:bg-cyan-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>

            {/* Learning Card - Middle Right */}
            <motion.div
              className="absolute"
              style={{ right: '20px', top: '380px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -12, 0],
                transition: { duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1.5 }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 3
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(3)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-500/30 to-red-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 3 ? "scale-110 neo-glow" : ""}`}>
                        {skills[3].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-orange-300 transition-colors duration-300 font-space-grotesk">
                        {skills[3].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[3].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-orange-500/10 text-orange-300 px-3 py-1 rounded-full text-xs border border-orange-500/20 transition-all duration-300 hover:bg-orange-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>

            {/* Soft Skills Card - Bottom Left */}
            <motion.div
              className="absolute"
              style={{ left: '50px', top: '530px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -9, 0],
                transition: { duration: 5.5, repeat: Infinity, ease: "easeInOut", delay: 2 }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 4
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(4)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-green-500/30 to-cyan-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 4 ? "scale-110 neo-glow" : ""}`}>
                        {skills[4].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-green-300 transition-colors duration-300 font-space-grotesk">
                        {skills[4].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[4].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-green-500/10 text-green-300 px-3 py-1 rounded-full text-xs border border-green-500/20 transition-all duration-300 hover:bg-green-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>

            {/* Other Card - Bottom Right */}
            <motion.div
              className="absolute"
              style={{ right: '50px', top: '530px' }}
              variants={itemVariants}
              animate={isInView ? {
                y: [0, -7, 0],
                transition: { duration: 4.8, repeat: Infinity, ease: "easeInOut", delay: 2.5 }
              } : {}}
            >
              <Card
                className={`group relative overflow-hidden transition-all duration-500 w-64 ${
                  hoveredIndex === 5
                    ? "neo-glow transform -translate-y-2 scale-105"
                    : "hover:transform hover:-translate-y-1"
                }`}
                onMouseEnter={() => setHoveredIndex(5)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-500/30 to-purple-500/30 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative bg-black/40 backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center text-center">
                      <div className={`mb-6 p-4 rounded-full bg-gradient-to-br from-black/60 to-black/20 border border-white/5 transition-all duration-500 ${hoveredIndex === 5 ? "scale-110 neo-glow" : ""}`}>
                        {skills[5].icon}
                      </div>
                      <h3 className="text-xl font-semibold mb-4 text-white group-hover:text-pink-300 transition-colors duration-300 font-space-grotesk">
                        {skills[5].category}
                      </h3>
                      <div className="flex flex-wrap justify-center gap-2 min-h-[60px] items-start">
                        {skills[5].technologies.map((tech, techIndex) => (
                          <span key={techIndex} className="bg-pink-500/10 text-pink-300 px-3 py-1 rounded-full text-xs border border-pink-500/20 transition-all duration-300 hover:bg-pink-500/20">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Add some spacing for the floating cards */}
      <div className="h-32"></div>
    </section>
  )
}

